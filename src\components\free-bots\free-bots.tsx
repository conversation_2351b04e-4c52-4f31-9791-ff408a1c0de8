import React from 'react';
import { observer } from 'mobx-react-lite';
import { useStore } from '@/hooks/useStore';
import { DBOT_TABS } from '@/constants/bot-contents';
import { localize } from '@deriv-com/translations';
import { load } from '@/external/bot-skeleton/scratch/utils';
import { save_types } from '@/external/bot-skeleton/constants';
import { botXmlData } from './bot-xml-data';
import './free-bots.scss';

interface BotCard {
    id: number;
    name: string;
    description: string;
    xmlKey: keyof typeof botXmlData;
    category: string;
}

const FreeBots: React.FC = observer(() => {
    const { dashboard } = useStore();
    const { setActiveTab } = dashboard;

    // Bot data with XML keys
    const botCards: BotCard[] = [
        {
            id: 1,
            name: 'Martingale Strategy',
            description: 'Classic martingale strategy that doubles stake after each loss',
            category: 'Risk Management',
            xmlKey: 'martingale'
        },
        {
            id: 2,
            name: 'Simple Call/Put Bot',
            description: 'Basic trading bot that alternates between call and put trades',
            category: 'Basic',
            xmlKey: 'simpleCallPut'
        },
        {
            id: 3,
            name: 'Advanced Martingale',
            description: 'Advanced martingale with profit threshold and loss management',
            category: 'Risk Management',
            xmlKey: 'advancedMartingale'
        },
        {
            id: 4,
            name: 'Three Candle Colors',
            description: 'Strategy based on analyzing three consecutive candle colors',
            category: 'Technical Analysis',
            xmlKey: 'threeCandleColors'
        },
        {
            id: 5,
            name: 'Last Digit Demo',
            description: 'Digit trading strategy based on last digit analysis',
            category: 'Digits',
            xmlKey: 'lastDigitDemo'
        },
        {
            id: 6,
            name: 'Random Bot',
            description: 'Simple random trading bot for testing purposes',
            category: 'Basic',
            xmlKey: 'randomBot'
        },
        {
            id: 7,
            name: 'Call/Put Alternator',
            description: 'Alternates between call and put trades automatically',
            category: 'Basic',
            xmlKey: 'callPutAlternator'
        }
    ];

    const handleBotClick = async (bot: BotCard) => {
        try {
            console.log(`Loading bot: ${bot.name}`);

            // Navigate to Bot Builder tab first
            setActiveTab(DBOT_TABS.BOT_BUILDER);

            // Small delay to ensure the tab has switched
            setTimeout(async () => {
                try {
                    // Get the XML content from the imported data
                    const xmlContent = botXmlData[bot.xmlKey];

                    // Load the XML content into the workspace
                    await load({
                        block_string: xmlContent,
                        file_name: bot.name,
                        workspace: window.Blockly.derivWorkspace,
                        from: save_types.LOCAL,
                        drop_event: {},
                        strategy_id: null,
                        showIncompatibleStrategyDialog: false,
                    });

                    console.log(`Successfully loaded bot: ${bot.name}`);
                } catch (loadError) {
                    console.error(`Error loading bot ${bot.name}:`, loadError);
                }
            }, 100);

        } catch (error) {
            console.error(`Error loading bot ${bot.name}:`, error);
        }
    };

    return (
        <div className="free-bots-container">
            <div className="free-bots-header">
                <h2>{localize('Free Bots')}</h2>
                <p>{localize('Click on any bot to automatically load it into the Bot Builder')}</p>
                <p className="free-bots-count">{localize(`${botCards.length} trading bots available`)}</p>
            </div>

            <div className="free-bots-grid">
                {botCards.map((bot) => (
                    <div
                        key={bot.id}
                        className="bot-card"
                        onClick={() => handleBotClick(bot)}
                        role="button"
                        tabIndex={0}
                        onKeyDown={(e) => {
                            if (e.key === 'Enter' || e.key === ' ') {
                                handleBotClick(bot);
                            }
                        }}
                    >
                        <div className="bot-card-content">
                            <div className="bot-card-icon">
                                <svg
                                    width="40"
                                    height="40"
                                    viewBox="0 0 24 24"
                                    fill="none"
                                    xmlns="http://www.w3.org/2000/svg"
                                >
                                    <path
                                        d="M12 2L2 7L12 12L22 7L12 2Z"
                                        stroke="currentColor"
                                        strokeWidth="2"
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                    />
                                    <path
                                        d="M2 17L12 22L22 17"
                                        stroke="currentColor"
                                        strokeWidth="2"
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                    />
                                    <path
                                        d="M2 12L12 17L22 12"
                                        stroke="currentColor"
                                        strokeWidth="2"
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                    />
                                </svg>
                            </div>
                            <div className="bot-card-info">
                                <div className="bot-card-category">{bot.category}</div>
                                <h3 className="bot-card-title">{bot.name}</h3>
                                <p className="bot-card-description">{bot.description}</p>
                            </div>
                        </div>
                        <div className="bot-card-overlay">
                            <span className="bot-card-action">{localize('Click to Load')}</span>
                        </div>
                    </div>
                ))}
            </div>
        </div>
    );
});

export default FreeBots;
